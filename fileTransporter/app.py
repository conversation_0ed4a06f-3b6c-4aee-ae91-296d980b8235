from datetime import datetime
from datetime import datetime, timedelta
import re
from flask import Flask, jsonify, render_template, send_from_directory, request, redirect, url_for, flash
import os

app = Flask(__name__)
app.secret_key = 'your-secret-key-here'  # 用于flash消息

# 设置你的文件目录路径
FILE_DIRECTORY = '/data'
TIME_DEVIATION_DIRECTORY = "/data"
UPLOAD_DIRECTORY = './uploads'  # 新增：上传文件目录（相对路径）

# 确保上传目录存在
os.makedirs(UPLOAD_DIRECTORY, exist_ok=True)

# 允许的文件扩展名
ALLOWED_EXTENSIONS = {'zip'}

def allowed_file(filename):
    """检查文件扩展名是否允许"""
    return '.' in filename and \
           filename.rsplit('.', 1)[1].lower() in ALLOWED_EXTENSIONS

@app.route('/')
def index():
    # 主页面，展示按钮
    return render_template('index.html')

#压缩文件下载
@app.route('/downloads')
def downloads():
    # 获取目录下的所有 .zip 文件
    files = [f for f in os.listdir(FILE_DIRECTORY) if f.endswith('.zip')]
    sorted_files = sorted(files, key=lambda f: (f.lower(), f))
    return render_template('downloads.html', files=sorted_files)


@app.route('/download/<filename>')
def download_file(filename):
    return send_from_directory(FILE_DIRECTORY, filename, as_attachment=True)

#时间同步文件下载
@app.route('/timeDeviationDownload')
def timeDeviationDownload():
    # 获取目录下的所有csv文件
    files = [f for f in os.listdir(TIME_DEVIATION_DIRECTORY) if f.endswith('.csv')]
    sorted_files = sorted(files,key=lambda f:(f.lower(),f))
    return render_template('downloads.html',files=sorted_files)

@app.route('/timeDeviationDownload/<filename>')
def timeDeviationDownload_file(filename):
    return send_from_directory(TIME_DEVIATION_DIRECTORY, filename, as_attachment=True)


#数据删除
@app.route('/delete')
def delete_page():
    # 获取目录下的所有 .zip 文件（供删除选择）
    # files = [f for f in os.listdir(FILE_DIRECTORY) if f.endswith('.zip')]
    old_files = []
    current_time = datetime.now()
    for filename in os.listdir(FILE_DIRECTORY):
        # 使用正则表达式匹配符合文件格式的文件
        match = re.match(r"^.*?_(\d{13})\.db(?:.*)?$", filename)
        if match:
            # 获取 UTC 时间戳字符串并转换为整数
            timestamp = int(match.group(1))

            # 将 13 位 UTC 时间戳转换为秒（减去后三位）
            file_time = datetime.fromtimestamp(timestamp / 1000.0)

            # 判断该文件时间是否早于48小时之前
            if current_time - file_time > timedelta(hours=48):
                old_files.append(filename)
        else:
            old_files.append(filename)
    
    # files = [f for f in os.listdir(FILE_DIRECTORY) ]
    sorted_files = sorted(old_files, key=lambda f: (f.lower(), f))






    return render_template('delete.html', files=sorted_files)


@app.route('/delete/<filename>', methods=['POST'])
def delete_file(filename):
    # 删除文件
    file_path = os.path.join(FILE_DIRECTORY, filename)
    if os.path.isfile(file_path):
        os.remove(file_path)
        return jsonify({"message": f"文件 {filename} 已成功删除"}), 200
    return jsonify({"message": f"文件 {filename} 不存在"}), 400

# 文件上传下载页面路由
@app.route('/upload_download')
def upload_download():
    """显示文件上传下载页面"""
    # 获取上传目录下的所有 .zip 文件
    files = []
    if os.path.exists(UPLOAD_DIRECTORY):
        files = [f for f in os.listdir(UPLOAD_DIRECTORY) if f.endswith('.zip')]
    sorted_files = sorted(files, key=lambda f: (f.lower(), f))
    return render_template('upload_download.html', files=sorted_files)

@app.route('/upload', methods=['POST'])
def upload_file():
    """处理文件上传"""
    if 'file' not in request.files:
        flash('没有选择文件')
        return redirect(url_for('upload_download'))

    file = request.files['file']
    if file.filename == '':
        flash('没有选择文件')
        return redirect(url_for('upload_download'))

    if file and allowed_file(file.filename):
        # 保留原始文件名，只做基本的安全处理
        original_filename = file.filename

        # 自定义安全文件名处理，保留更多原始信息
        filename = original_filename
        # 移除路径分隔符和其他危险字符，但保留中文和常见字符
        import re
        filename = re.sub(r'[<>:"/\\|?*]', '_', filename)
        filename = filename.strip()

        # 确保文件名不为空且有扩展名
        if not filename or filename == '.zip':
            filename = f"uploaded_file_{datetime.now().strftime('%Y%m%d_%H%M%S')}.zip"

        file_path = os.path.join(UPLOAD_DIRECTORY, filename)

        # 如果文件已存在，添加时间戳避免覆盖
        if os.path.exists(file_path):
            name, ext = os.path.splitext(filename)
            timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
            filename = f"{name}_{timestamp}{ext}"
            file_path = os.path.join(UPLOAD_DIRECTORY, filename)

        try:
            file.save(file_path)
            flash(f'文件 {filename} 上传成功！')
        except Exception as e:
            flash(f'文件上传失败: {str(e)}')
    else:
        flash('只允许上传 .zip 文件')

    return redirect(url_for('upload_download'))

@app.route('/delete_upload/<filename>', methods=['POST'])
def delete_upload_file(filename):
    """删除上传目录中的文件"""
    file_path = os.path.join(UPLOAD_DIRECTORY, filename)
    if os.path.isfile(file_path):
        os.remove(file_path)
        return jsonify({"message": f"文件 {filename} 已成功删除"}), 200
    return jsonify({"message": f"文件 {filename} 不存在"}), 400

def delete_file(filename, directory='./'):
    """
    删除指定目录下的文件。

    :param filename: 要删除的文件名
    :param directory: 文件所在目录，默认为当前目录
    :return: 成功删除返回 True，否则返回 False 和错误信息
    """
    file_path = os.path.join(directory, filename)

    try:
        # 检查文件是否存在
        if os.path.isfile(file_path):
            os.remove(file_path)
            print(f"文件 {filename} 已成功删除。")
            return True
        else:
            print(f"文件 {filename} 不存在。")
            return False
    except Exception as e:
        print(f"删除文件时发生错误: {e}")
        return False
    
if __name__ == '__main__':
    app.run(host='0.0.0.0', port=5000)

