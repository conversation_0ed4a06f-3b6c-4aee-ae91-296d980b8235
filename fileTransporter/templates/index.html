<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>文件管理</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            margin: 0;
            padding-top: 70px; /* 为导航栏留出空间 */
            background-color: #f4f4f4;
        }
        .navbar {
            position: fixed;
            top: 0;
            left: 0;
            width: 100%;
            height: 60px;
            background-color: #333;
            color: #fff;
            display: flex;
            align-items: center;
            justify-content: space-between;
            padding: 0 20px;
            box-shadow: 0 2px 5px rgba(0, 0, 0, 0.2);
            z-index: 1000;
        }
        .navbar .left {
            display: flex;
            align-items: center;
        }
        .logo {
            height: 40px;
            margin-right: 15px;
        }
        .title {
            position: absolute;
            left: 50%;
            transform: translateX(-50%);
            font-size: 20px;
            color: #fff;
            margin: 0;
        }
        .container {
            width: 100%;
            max-width: 1200px; /* 设置一个最大宽度 */
            margin: 0 auto;
            padding: 20px;
            box-sizing: border-box;
        }
        ul {
            list-style-type: none;
            padding: 0;
            margin: 0;
            display: flex;
            flex-direction: column;
        }
        li {
            font-size: 18px;
            margin: 5px 10px 5px 10px;
            padding: 15px;
            border-radius: 5px;
            background-color: #fff;
            box-shadow: 0 2px 5px rgba(0, 0, 0, 0.1);
            cursor: pointer;
            transition: background-color 0.3s;
        }
        li:hover {
            background-color: #e0e0e0;
        }
        /* 让整个 li 项可点击 */
        li a {
            text-decoration: none;
            color: #333;
            display: block;
            width: 100%;
            height: 100%;
        }
        /* 按钮 */
        /* 简单的样式来居中和美化按钮 */
        .button-container {
            display: flex;
            flex-wrap: wrap; /* 自动换行 */
            justify-content: center; /* 按钮水平居中 */
            gap: 20px; /* 按钮之间的水平和垂直间距 */
            margin-top: 20px;
        }

         /* 按钮的基本样式 */
         .button {
            width: 128px; /* 设置正方形尺寸 */
            height: 158px;
            display: flex;
            flex-direction: column; /* 使图像和文字垂直排列 */
            justify-content: center;
            align-items: center;
            font-size: 1.2em;
            cursor: pointer;
            color: #a9a9a9;
            border: none;
            border-radius: 5px;
            border-color: #a9a9a9;
            text-align: center;
        }

        .download-btn {
            background-color: #ffffff;
        }

        .delete-btn {
            background-color: #ffffff;
        }

        /* 图像样式 */
        .button img {
            width: 80%; /* 调整图像大小 */
            height: auto;
            margin-bottom: 10px; /* 图像与文字的间距 */
        }

    </style>
</head>
<body>
    <!-- 固定的导航栏 -->
    <header class="navbar">
        <div class="left">
            <img src="{{ url_for('static', filename='logo.png') }}" alt="Logo" class="logo">
        </div>
        <!-- <h1 class="title">文件下载</h1> -->
    </header>
    <div class="button-container">
        <div class="button-container">
            <!-- 下载按钮，带图像和文字 -->
            <button class="button download-btn" onclick="location.href='{{ url_for('downloads') }}'">
                <img src="{{ url_for('static', filename='download.png') }}" alt="Download Icon">
                压缩打包<br>文件下载
            </button>

             <!-- 下载按钮，带图像和文字 -->
             <button class="button download-btn" onclick="location.href='{{ url_for('timeDeviationDownload') }}'">
                <img src="{{ url_for('static', filename='download.png') }}" alt="Download Icon">
                时间同步<br>文件下载
            </button>

            <!-- 文件上传管理按钮，带图像和文字 -->
            <button class="button download-btn" onclick="location.href='{{ url_for('upload_download') }}'">
                <img src="{{ url_for('static', filename='download.png') }}" alt="Upload Management Icon">
                文件上传<br>管理
            </button>

            <!-- 删除按钮，带图像和文字 -->
            <button class="button delete-btn" onclick="location.href='{{ url_for('delete_page') }}'">
                <img src="{{ url_for('static', filename='delete.png') }}" alt="Delete Icon">
                数 据<br>删 除
            </button>
        </div>
        <!-- <button class="button download-btn" onclick="location.href='{{ url_for('downloads') }}'">下载文件</button>
        <button class="button delete-btn" onclick="location.href='{{ url_for('delete_page') }}'">删除文件</button> -->
    </div>
    <!-- 文件列表的容器，宽度充满整个页面-->
    <!-- <div class="container"> -->
        <!-- <ul> -->
            <!-- {% for file in files %} -->
            <!-- <li> -->
                <!-- 将整个 li 作为链接 -->
                <!-- <a href="{{ url_for('download_file', filename=file) }}">{{ file }}</a> -->
            <!-- </li> -->
            <!-- {% endfor %} -->
        <!-- </ul> -->
    <!-- </div> --> 
</body>
</html>

