<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>文件上传下载</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            margin: 0;
            padding-top: 70px; /* 为导航栏留出空间 */
            background-color: #f4f4f4;
        }
        .navbar {
            position: fixed;
            top: 0;
            left: 0;
            width: 100%;
            height: 60px;
            background-color: #333;
            color: #fff;
            display: flex;
            align-items: center;
            justify-content: space-between;
            padding: 0 20px;
            box-shadow: 0 2px 5px rgba(0, 0, 0, 0.2);
            z-index: 1000;
        }
        .navbar .left {
            display: flex;
            align-items: center;
        }
        .logo {
            height: 40px;
            margin-right: 15px;
        }
        .title {
            position: absolute;
            left: 50%;
            transform: translateX(-50%);
            font-size: 20px;
            color: #fff;
            margin: 0;
        }
        .container {
            width: 100%;
            max-width: 1200px; /* 设置一个最大宽度 */
            margin: 0 auto;
            padding: 20px;
            box-sizing: border-box;
        }
        
        /* 上传区域样式 */
        .upload-section {
            background-color: #fff;
            padding: 20px;
            margin-bottom: 30px;
            border-radius: 5px;
            box-shadow: 0 2px 5px rgba(0, 0, 0, 0.1);
        }
        .upload-section h2 {
            margin-top: 0;
            color: #333;
        }
        .upload-form {
            display: flex;
            align-items: center;
            gap: 15px;
            flex-wrap: wrap;
        }
        .file-input-wrapper {
            position: relative;
            overflow: hidden;
            display: inline-block;
        }
        .file-input-wrapper input[type=file] {
            position: absolute;
            left: -9999px;
        }
        .file-input-label {
            padding: 10px 20px;
            background-color: #007bff;
            color: white;
            border-radius: 4px;
            cursor: pointer;
            transition: background-color 0.3s;
        }
        .file-input-label:hover {
            background-color: #0056b3;
        }
        .upload-btn {
            padding: 10px 20px;
            background-color: #28a745;
            color: white;
            border: none;
            border-radius: 4px;
            cursor: pointer;
            transition: background-color 0.3s;
        }
        .upload-btn:hover {
            background-color: #218838;
        }
        .upload-btn:disabled {
            background-color: #6c757d;
            cursor: not-allowed;
        }
        .selected-file {
            color: #666;
            font-style: italic;
        }
        
        /* Flash 消息样式 */
        .flash-messages {
            margin-bottom: 20px;
        }
        .flash-message {
            padding: 10px 15px;
            margin-bottom: 10px;
            border-radius: 4px;
            background-color: #d4edda;
            color: #155724;
            border: 1px solid #c3e6cb;
        }
        .flash-message.error {
            background-color: #f8d7da;
            color: #721c24;
            border-color: #f5c6cb;
        }
        
        /* 文件列表样式 */
        .files-section h2 {
            color: #333;
            margin-bottom: 15px;
        }
        ul {
            list-style-type: none;
            padding: 0;
            margin: 0;
            display: flex;
            flex-direction: column;
        }
        li {
            font-size: 18px;
            margin: 5px 10px 5px 10px;
            padding: 15px;
            border-radius: 5px;
            background-color: #fff;
            box-shadow: 0 2px 5px rgba(0, 0, 0, 0.1);
            cursor: pointer;
            transition: background-color 0.3s;
        }
        li:hover {
            background-color: #e0e0e0;
        }
        /* 让整个 li 项可点击 */
        li a {
            text-decoration: none;
            color: #333;
            display: block;
            width: 100%;
            height: 100%;
        }
        
        /* 返回按钮 */
        .back-btn {
            display: inline-block;
            padding: 10px 20px;
            background-color: #6c757d;
            color: white;
            text-decoration: none;
            border-radius: 4px;
            margin-bottom: 20px;
            transition: background-color 0.3s;
        }
        .back-btn:hover {
            background-color: #545b62;
        }
    </style>
</head>
<body>
    <!-- 固定的导航栏 -->
    <header class="navbar">
        <div class="left">
            <img src="{{ url_for('static', filename='logo.png') }}" alt="Logo" class="logo">
        </div>
    </header>
    
    <div class="container">
        <!-- 返回主页按钮 -->
        <a href="{{ url_for('index') }}" class="back-btn">← 返回主页</a>
        
        <!-- Flash 消息 -->
        {% with messages = get_flashed_messages() %}
            {% if messages %}
                <div class="flash-messages">
                    {% for message in messages %}
                        <div class="flash-message">{{ message }}</div>
                    {% endfor %}
                </div>
            {% endif %}
        {% endwith %}
        
        <!-- 文件上传区域 -->
        <div class="upload-section">
            <h2>上传 ZIP 文件</h2>
            <form method="post" enctype="multipart/form-data" action="{{ url_for('upload_file') }}" class="upload-form">
                <div class="file-input-wrapper">
                    <input type="file" id="file" name="file" accept=".zip" onchange="updateFileName()">
                    <label for="file" class="file-input-label">选择文件</label>
                </div>
                <span id="selected-file" class="selected-file">未选择文件</span>
                <button type="submit" class="upload-btn" id="upload-btn" disabled>上传文件</button>
            </form>
        </div>
        
        <!-- 文件列表区域 -->
        <div class="files-section">
            <h2>可下载文件 ({{ files|length }} 个文件)</h2>
            {% if files %}
                <ul>
                    {% for file in files %}
                        <li><a href="{{ url_for('download_upload_file', filename=file) }}">{{ file }}</a></li>
                    {% endfor %}
                </ul>
            {% else %}
                <p style="color: #666; font-style: italic; text-align: center; padding: 20px;">暂无文件</p>
            {% endif %}
        </div>
    </div>
    
    <script>
        function updateFileName() {
            const fileInput = document.getElementById('file');
            const selectedFileSpan = document.getElementById('selected-file');
            const uploadBtn = document.getElementById('upload-btn');
            
            if (fileInput.files.length > 0) {
                selectedFileSpan.textContent = fileInput.files[0].name;
                uploadBtn.disabled = false;
            } else {
                selectedFileSpan.textContent = '未选择文件';
                uploadBtn.disabled = true;
            }
        }
    </script>
</body>
</html>
