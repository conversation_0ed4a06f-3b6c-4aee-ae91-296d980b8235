<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Delete Files</title>
    <script>
        // 删除文件的 JavaScript 函数，包含确认弹窗
        function confirmDelete(filename) {
            // 显示确认弹窗
            if (confirm(`确定要删除文件 ${filename} 吗？`)) {
                deleteFile(filename);
            }
        }

        // 执行删除操作的函数
        function deleteFile(filename) {
            fetch(`/delete/${filename}`, {
                method: 'POST'
            })
            .then(response => response.json())
            .then(data => {
                alert(data.message);  // 显示删除结果
                location.reload();  // 刷新页面以更新文件列表
            })
            .catch(error => {
                console.error('Error:', error);
                alert("删除文件时出错");
            });
        }
    </script>
    <style>
        body {
            font-family: Arial, sans-serif;
            margin: 0;
            padding-top: 70px; /* 为导航栏留出空间 */
            background-color: #f4f4f4;
        }
        .navbar {
            position: fixed;
            top: 0;
            left: 0;
            width: 100%;
            height: 60px;
            background-color: #333;
            color: #fff;
            display: flex;
            align-items: center;
            justify-content: space-between;
            padding: 0 20px;
            box-shadow: 0 2px 5px rgba(0, 0, 0, 0.2);
            z-index: 1000;
        }
        .navbar .left {
            display: flex;
            align-items: center;
        }
        .logo {
            height: 40px;
            margin-right: 15px;
        }
        .title {
            position: absolute;
            left: 50%;
            transform: translateX(-50%);
            font-size: 20px;
            color: #fff;
            margin: 0;
        }
        .container {
            width: 100%;
            max-width: 1200px; /* 设置一个最大宽度 */
            margin: 0 auto;
            padding: 20px;
            box-sizing: border-box;
        }
        ul {
            list-style-type: none;
            padding: 0;
            margin: 0;
            display: flex;
            flex-direction: column;
        }
        li {
            font-size: 18px;
            margin: 5px 10px 5px 10px;
            padding: 15px;
            border-radius: 5px;
            background-color: #fff;
            box-shadow: 0 2px 5px rgba(0, 0, 0, 0.1);
            cursor: pointer;
            transition: background-color 0.3s;
        }
        li:hover {
            background-color: #e0e0e0;
        }
        /* 让整个 li 项可点击 */
        li a {
            text-decoration: none;
            color: #333;
            display: block;
            width: 100%;
            height: 100%;
        }

        /* 返回按钮 */
        .back-btn {
            display: inline-block;
            padding: 10px 20px;
            background-color: #6c757d;
            color: white;
            text-decoration: none;
            border-radius: 4px;
            margin-bottom: 20px;
            transition: background-color 0.3s;
        }
        .back-btn:hover {
            background-color: #545b62;
        }
    </style>
</head>
<body>
    <!-- 固定的导航栏 -->
    <header class="navbar">
        <div class="left">
            <img src="{{ url_for('static', filename='logo.png') }}" alt="Logo" class="logo">
        </div>
        <!-- <h1 class="title">文件下载</h1> -->
    </header>

    <div class="container">
        <!-- 返回主页按钮 -->
        <a href="{{ url_for('index') }}" class="back-btn">← 返回主页</a>

        <h1>可删除文件</h1>
        <ul>
            {% for file in files %}
                <!-- 整个 li 项可点击，触发 confirmDelete 函数 -->
                <li onclick="confirmDelete('{{ file }}')">{{ file }}</li>
            {% endfor %}
        </ul>
    </div>
</body>
</html>
